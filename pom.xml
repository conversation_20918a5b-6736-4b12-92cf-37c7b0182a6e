<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ctbiyi</groupId>
        <artifactId>biyi-parent</artifactId>
        <version>4.8.0</version>
    </parent>

    <groupId>com.ctsi.hndxoa</groupId>
    <artifactId>hndxoa</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <description>湖南集成公司办公新平台</description>
    <name>hndxoa</name>

    <properties>
        <!-- 模块1 2   -->

        <hndxoa.version>1.0.0-SNAPSHOT</hndxoa.version>
        <hndxoa.basebiyicorepom.version>1.0.0-SNAPSHOT</hndxoa.basebiyicorepom.version>
        <hndxoa.base.version>1.0.0-SNAPSHOT</hndxoa.base.version>
        <hndxoa.userorg.version>1.0.0-SNAPSHOT</hndxoa.userorg.version>
        <hndxoa.system.version>1.0.0-SNAPSHOT</hndxoa.system.version>
        <!--<hndxoa.activiti.version>1.0.0-SNAPSHOT</hndxoa.activiti.version>
        <hndxoa.cform.version>1.0.0-SNAPSHOT</hndxoa.cform.version> -->
        <hndxoa.web.api.version>1.0.0-SNAPSHOT</hndxoa.web.api.version>
        <hndxoa.file.operation.version>1.0.0-SNAPSHOT</hndxoa.file.operation.version>
        <hndxoa.sms>1.0.0-SNAPSHOT</hndxoa.sms>
        <hndxoa.import>1.0.0-SNAPSHOT</hndxoa.import>
        <hndxoa.auto.generator.version>1.0.0-SNAPSHOT</hndxoa.auto.generator.version>
        <!--  biyi-->
        <com.ctbiyi.component-sdk.version>1.0.37</com.ctbiyi.component-sdk.version>

        <!-- 其他的 -->
        <failOnMissingWebXml>false</failOnMissingWebXml>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <imageVersion>1.0.0</imageVersion>
        <imageName>image.docker.ssdc.solutions/ctsi/hnedu-default</imageName>
        <com.baomidou.version>3.4.1</com.baomidou.version>
        <org.mapstruct.version>1.4.2.Final</org.mapstruct.version>
        <org.projectlombok.version>1.18.22</org.projectlombok.version>
        <autopoi-web>2.0.1</autopoi-web>
        <tomcat.version>9.0.24</tomcat.version>
        <!-- 强制统一Netty版本，避免冲突 - 兼容Spring Boot 2.2.5和ShenYu -->
        <netty.version>4.1.45.Final</netty.version>
        <!-- 使用Spring Boot 2.2.5默认的Reactor Netty版本 -->
        <reactor-netty.version>0.9.5.RELEASE</reactor-netty.version>
        <!-- ShenYu版本 -->
        <shenyu.version>2.6.1</shenyu.version>

        <!-- 升级关键依赖版本以提高稳定性 -->
        <spring-amqp.version>2.2.21.RELEASE</spring-amqp.version>
        <spring-rabbit.version>2.2.21.RELEASE</spring-rabbit.version>
        <rabbitmq-amqp-client.version>5.9.0</rabbitmq-amqp-client.version>
        <fastjson.version>1.2.83</fastjson.version>
    </properties>

    <modules>
        <module>hndxoa-basebiyicorepom</module>
        <module>hndxoa-base</module>
        <module>hndxoa-system</module>
        <module>hndxoa-file-operation</module>
        <module>hndxoa-userorg</module>
<!--
        <module>hndxoa-auto-generator</module>
        <module>hndxoa-cform</module>
        <module>hndxoa-activiti</module>
-->
        <module>hndxoa-business</module>
        <module>hndxoa-web-api</module>
        <module>hndxoa-sms</module>
        <module>hndxoa-import</module>
<!--        <module>hndxoa-sw-meeting</module>-->

                <module>hndxoa-middleware</module>


    </modules>


    <dependencyManagement>

        <dependencies>
            <dependency>
                <groupId>com.ctsi.hndxoa</groupId>
                <artifactId>hndxoa-basebiyicorepom</artifactId>
                <version>${hndxoa.basebiyicorepom.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctsi.hndxoa</groupId>
                <artifactId>hndxoa-base</artifactId>
                <version>${hndxoa.base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctsi.hndxoa</groupId>
                <artifactId>hndxoa-userorg</artifactId>
                <version>${hndxoa.userorg.version}</version>
            </dependency>
            <!-- <dependency>
                <groupId>com.ctsi.hndxoa</groupId>
                <artifactId>hndxoa-activiti</artifactId>
                <version>${hndxoa.activiti.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctsi.hndxoa</groupId>
                <artifactId>hndxoa-cform</artifactId>
                <version>${hndxoa.cform.version}</version>
            </dependency>-->
            <dependency>
                <groupId>com.ctsi.hndxoa</groupId>
                <artifactId>hndxoa-web-api</artifactId>
                <version>${hndxoa.web.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctsi.hndxoa</groupId>
                <artifactId>hndxoa-file-operation</artifactId>
                <version>${hndxoa.file.operation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctsi.hndxoa</groupId>
                <artifactId>hndxoa-system</artifactId>
                <version>${hndxoa.file.operation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctsi.hndxoa</groupId>
                <artifactId>hndxoa-sms</artifactId>
                <version>${hndxoa.sms}</version>
            </dependency>
            <dependency>
                <groupId>com.ctsi.hndxoa</groupId>
                <artifactId>hndxoa-import</artifactId>
                <version>${hndxoa.import}</version>
            </dependency>
            <dependency>
                <groupId>com.ctsi.hndxoa</groupId>
                <artifactId>hndxoa-middleware</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <!--  <dependency>
                  <groupId>com.ctsi.hndxoa</groupId>
                  <artifactId>hndxoa-notice</artifactId>
                  <version>${hndxoa.file.operation.version}</version>
              </dependency>
              <dependency>
                  <groupId>com.ctsi.hndxoa</groupId>
                  <artifactId>hndxoa-document-circulate</artifactId>
                  <version>${hndxoa.file.operation.version}</version>
              </dependency>-->


            <dependency>
                <groupId>com.ctbiyi</groupId>
                <artifactId>component-sdk</artifactId>
                <version>${com.ctbiyi.component-sdk.version}</version>
            </dependency>

            <!-- ctbiyi相关 -->
            <dependency>
                <groupId>com.ctbiyi</groupId>
                <artifactId>biyi-ffcs-redisutil</artifactId>
                <version>1.0.1</version>
            </dependency>

            <!-- mybatis -->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-typehandlers-jsr310</artifactId>
                <version>2.0.0</version>
            </dependency>

            <!-- mybatis-plus -->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-typehandlers-jsr310</artifactId>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${com.baomidou.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${com.baomidou.version}</version>
            </dependency>


            <!-- mapstruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <!-- com.alibaba -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>

            <!-- xxl-job -->
            <!--<dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>2.3.0</version>
            </dependency>-->

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>

            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>8.3.3</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.9.1</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.5.14</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>1.5.14</version>
                <scope>compile</scope>
            </dependency>

            <!-- <dependency>
                <groupId>org.activiti</groupId>
                <artifactId>activiti-spring-boot-starter-basic</artifactId>
                <version>6.0.0</version>

            </dependency>
            <dependency>
                <groupId>org.activiti</groupId>
                <artifactId>activiti-spring-boot-starter-rest-api</artifactId>
                <version>6.0.0</version>
            </dependency>-->
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>3.3.0</version>
            </dependency>


            <!--            <dependency>-->
            <!--                <groupId>dianxinoa</groupId>-->
            <!--                <artifactId>oss-java-sdk</artifactId>-->
            <!--                <version>1.0</version>-->
            <!--            </dependency>-->


            <dependency>
                <groupId>org.modelmapper</groupId>
                <artifactId>modelmapper</artifactId>
                <version>2.3.0</version>
            </dependency>

            <!--Excel工具-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.0.5</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>5.7.15</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-extra</artifactId>
                <version>5.7.15</version>
            </dependency>

            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>2.5.0</version>
            </dependency>


            <!-- 二维码支持包 -->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.3.0</version>
            </dependency>




            <dependency>
                <groupId>com.ctbiyi</groupId>
                <artifactId>biyi-ffcs-redisutil</artifactId>
                <version>1.0.1</version>
            </dependency>

            <!-- 因仓库没有，来自比翼平台制品库上传 -->
            <dependency>
                <groupId>com.ctsi.hndx</groupId>
                <artifactId>oss-java-sdk</artifactId>
                <version>1.0.0</version>
            </dependency>

            <!--  xxl-job   -->
            <!--<dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>2.3.0</version>
            </dependency>
            <dependency>
                <groupId>com.ctsi.hndxoa</groupId>
                <artifactId>hndxoa-meeting</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>-->

            <dependency>
                <groupId>fr.opensagres.xdocreport</groupId>
                <artifactId>fr.opensagres.xdocreport.document.docx</artifactId>
                <version>2.0.1</version>
            </dependency>
            <dependency>
                <groupId>fr.opensagres.xdocreport</groupId>
                <artifactId>fr.opensagres.xdocreport.template.freemarker</artifactId>
                <version>2.0.1</version>
            </dependency>
            <!--<dependency>
                <groupId>com.ctsi.hndxoa</groupId>
                <artifactId>hndxoa-currency</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.ctsi.hndxoa</groupId>
                <artifactId>hndxoa-distribute</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>-->
            <!--国密ms4-->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15to18</artifactId>
                <version>1.69</version>
            </dependency>

            <!--Java连接池版客户端-->
            <dependency>
                <groupId>com.ctg.itrdc.cache</groupId>
                <artifactId>ctg-cache-nclient</artifactId>
                <version>2.7.9</version>
            </dependency>

            <!--Jedis依赖-->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>3.2.0</version>
            </dependency>

            <!--多数据源切换-->
<!--            <dependency>-->
<!--                <groupId>com.baomidou</groupId>-->
<!--                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>-->
<!--                <version>3.4.1</version>-->
<!--            </dependency>-->

            <!-- 强制统一Netty版本管理，解决ShenYu兼容性问题 -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>${netty.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 升级Reactor Netty以支持ShenYu所需的ProxyProvider$Builder -->
            <dependency>
                <groupId>io.projectreactor.netty</groupId>
                <artifactId>reactor-netty</artifactId>
                <version>${reactor-netty.version}</version>
            </dependency>

            <!-- 注意：Spring Boot 2.2.5中reactor-netty还没有分离为core和http模块 -->

            <!-- ShenYu依赖管理 -->
            <dependency>
                <groupId>org.apache.shenyu</groupId>
                <artifactId>shenyu-spring-boot-starter-plugin-divide</artifactId>
                <version>${shenyu.version}</version>
            </dependency>

            <!-- 升级RabbitMQ相关依赖以提高稳定性 -->
            <dependency>
                <groupId>org.springframework.amqp</groupId>
                <artifactId>spring-amqp</artifactId>
                <version>${spring-amqp.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.amqp</groupId>
                <artifactId>spring-rabbit</artifactId>
                <version>${spring-rabbit.version}</version>
            </dependency>

            <dependency>
                <groupId>com.rabbitmq</groupId>
                <artifactId>amqp-client</artifactId>
                <version>${rabbitmq-amqp-client.version}</version>
            </dependency>

            <!-- 升级FastJSON以修复安全漏洞和提高稳定性 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- MyBatis Plus版本已在com.baomidou.version中统一管理 -->

        </dependencies>

    </dependencyManagement>


    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/**</include>
                </includes>
                <excludes>
                    <exclude>**/*.lc</exclude>
                </excludes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.lc</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/webapp</directory>
                <targetPath>META-INF/resources</targetPath>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
        </resources>

        <pluginManagement>
            <plugins>
                <!-- compiler插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <!--  打包插件 -->
                <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${biyi.springboot.version}</version>
                </plugin>
                <!--  打镜像插件 -->
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>dockerfile-maven-plugin</artifactId>
                    <version>1.3.6</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>doc</nonFilteredFileExtension>
                        <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>zip</nonFilteredFileExtension>
                        <nonFilteredFileExtension>so</nonFilteredFileExtension>
                        <nonFilteredFileExtension>so.10</nonFilteredFileExtension>
                        <nonFilteredFileExtension>se</nonFilteredFileExtension>
                        <nonFilteredFileExtension>asn</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jks</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>

            <!--  compiler插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${org.projectlombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-jdk8</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>yanfayun</id>
            <name>local private nexus release</name>
            <url>https://artifact.srdcloud.cn/artifactory/hndxoa-release-maven-virtual</url>
<!--            <url>http://134.175.222.203:8081/repository/maven-snapshots/</url>-->
        </repository>

       <!-- <repository>
            <id>nexus-public-bantongwang</id>
            <name>local private nexus release</name>
            <url>http://134.175.222.203:8081/repository/maven-public/</url>

        </repository>
-->
    </repositories>

    <distributionManagement>


        <repository>
            <id>nexus-release</id>
            <name>local private nexus release</name>
            <url>http://134.175.222.203:8081/repository/maven-snapshots/</url>
        </repository>

        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>local private nexus snapshots</name>
            <url>http://134.175.222.203:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>


    <pluginRepositories>

        <pluginRepository>
            <id>nexus-public</id>
            <name>Team Nexus Repository</name>
            <url>https://artifact.srdcloud.cn/artifactory/hnict_oa-snapshot-maven-virtual</url>
        </pluginRepository>
    </pluginRepositories>
</project>
