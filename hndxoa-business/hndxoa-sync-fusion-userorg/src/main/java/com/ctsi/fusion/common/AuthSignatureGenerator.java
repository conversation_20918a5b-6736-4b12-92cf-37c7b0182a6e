package com.ctsi.fusion.common;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AuthSignatureGenerator {

    public static void main(String[] args) {
        // Step 1: 构造一个 Map
        Map<String, String> map = new HashMap<>();
        map.put("timestamp", String.valueOf(LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli()));
        map.put("path", "/api/api/foreign/cscpOrg/pull");
        map.put("version", "1.0.0");

        // Step 2: 进行 Key 的自然排序，然后 Key，Value值拼接最后再拼接分配给你的 SK
        List<String> storedKeys = Arrays.stream(map.keySet().toArray(new String[]{}))
                .sorted(Comparator.naturalOrder())
                .collect(java.util.stream.Collectors.toList());

        final String appKey = "6B9D01C72C9B4E56AB25D893AAEC08FF";
        final String sk = "94EF7D8E0CFC4E14AF61D07102FE0970";

        final String sign = storedKeys.stream()
                .map(key -> String.join("", key, map.get(key)))
                .collect(java.util.stream.Collectors.joining()).trim()
                .concat(sk);

        // Step 3: 进行 MD5 加密后转成大写
        String md5Sign = "";
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(sign.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02X", b));
            }
            md5Sign = sb.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }

        System.out.println("timestamp: " + map.get("timestamp"));
        System.out.println("appKey: " + appKey);
        System.out.println("sign: " + md5Sign);
        System.out.println("version: " + map.get("version"));
    }





}



