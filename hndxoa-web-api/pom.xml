<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hndxoa</artifactId>
        <groupId>com.ctsi.hndxoa</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ctsi.hndxoa</groupId>
    <artifactId>hndxoa-web-api</artifactId>
    <version>${hndxoa.web.api.version}</version>
    <packaging>jar</packaging>
    <description>业务系统的web接口</description>

    <dependencies>


        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-basebiyicorepom</artifactId>
            <exclusions>
                <!--<exclusion>
                    <artifactId>liquibase-core</artifactId>
                    <groupId>org.liquibase</groupId>
                </exclusion>-->
                <exclusion>
                    <groupId>org.mybatis.spring.boot</groupId>
                    <artifactId>mybatis-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>mapstruct</artifactId>
                    <groupId>org.mapstruct</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsqlparser</artifactId>
                    <groupId>com.github.jsqlparser</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-typehandlers-jsr310</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <!-- <exclusions>
                 <exclusion>
                     <artifactId>jsqlparser</artifactId>
                     <groupId>com.github.jsqlparser</groupId>
                 </exclusion>
             </exclusions>-->
        </dependency>
        <!--<dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>-->
        <dependency>
            <groupId>com.ctbiyi</groupId>
            <artifactId>biyi-captcha</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctbiyi</groupId>
                    <artifactId>biyi-util</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctbiyi</groupId>
                    <artifactId>component-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctbiyi</groupId>
                    <artifactId>component-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-base</artifactId>
        </dependency>

        <!--<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-activiti</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->

        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-system</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 添加文件操作模块依赖 -->
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-file-operation</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-notice</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>third-party</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->


        <!--<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-news</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->


        <!--<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-distribute</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-file-operation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->

        <!--<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-leader-schedule</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>-->
        <!--领导外出请示报备-->
        <!--<dependency>
           <groupId>com.ctsi.hndxoa</groupId>
           <artifactId>hndxoa-leader-outgo</artifactId>
           <version>1.0.0-SNAPSHOT</version>
           <exclusions>
               <exclusion>
                   <groupId>com.ctsi.hndxoa</groupId>
                   <artifactId>hndxoa-base</artifactId>
               </exclusion>
           </exclusions>
       </dependency>-->

        <!--<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-attendance-management</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
           <groupId>com.ctsi.hndxoa</groupId>
           <artifactId>hndxoa-document-circulate</artifactId>
           <version>1.0.0-SNAPSHOT</version>
           <exclusions>
               <exclusion>
                   <groupId>com.ctsi.hndxoa</groupId>
                   <artifactId>hndxoa-base</artifactId>
               </exclusion>
           </exclusions>
       </dependency>-->


       <!-- 发文 -->
        <!--<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-senddoc-master</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->

        <!-- 收文 -->
        <!--<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-receive-management</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->
        <!-- 领导外出 -->
        <!--<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-leader-outgo</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->

        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-sms</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 领导外出 -->
        <!--<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-leader-outgo</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->

       <!-- <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-meeting</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->


     <!--   <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-cloud-disk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        &lt;!&ndash; 常委会会议  &ndash;&gt;
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-committee-meeting</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->

        <!--<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-vehicle-management</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
-->
        <!--<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-archive-management</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->

       <!-- <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-huaihua-government-efficiency</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->

      <!--  <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-video-cloud</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->

       <!-- <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-international-office</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <!--JavaServer Pages Standard Tag Library，JSP标准标签库-->
       <!-- <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>jstl</artifactId>
        </dependency>-->

        <!--内置tocat对Jsp支持的依赖，用于编译Jsp-->
       <!-- <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-jasper</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-emergency-message</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>-->

      <!--  <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-third-party</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->
        <!-- 每日汇报 -->
        <!--<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-daily-report</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->
        <!-- 投票问卷 -->
       <!-- <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-question</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        &lt;!&ndash; 领导外出 &ndash;&gt;
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-leader-outgo</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        &lt;!&ndash; 办公邮件 &ndash;&gt;
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-officemail</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        &lt;!&ndash; 无纸会议 &ndash;&gt;
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-paperless-conference</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        &lt;!&ndash;<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-currency</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>&ndash;&gt;

        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-dahua-meeting</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
-->
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-swoa</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-sw-meeting</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->



       <!-- &lt;!&ndash; 外出请示功能模块 &ndash;&gt;
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-outing-ask</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->
       <!-- <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-entering_hunan_filing</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-distribute</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->

        <!--<dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-law-approvalpro</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-person-matters</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-gwhd</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-file-operation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctsi.swsfw</groupId>
            <artifactId>hndxoa-ldgzsx</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-file-operation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-org-user-sync</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-file-operation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-sync-fusion-userorg</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-base</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctsi.hndxoa</groupId>
                    <artifactId>hndxoa-file-operation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--多数据源切换-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.5.2</version>
        </dependency>

        <!-- 卫士通密码机库文件 -->
        <dependency>
            <groupId>com.westone</groupId>
            <artifactId>mina-core</artifactId>
            <version>2.0.5</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/../lib/WST/mina-core-2.0.5.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.westone</groupId>
            <artifactId>cetccst-hsm-api</artifactId>
            <version>1.10.10</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/../lib/WST/cetccst-hsm-api-1.10.10.jar</systemPath>
        </dependency>

        <!--if you use http proxy start this - 使用统一版本管理-->
        <dependency>
            <groupId>org.apache.shenyu</groupId>
            <artifactId>shenyu-spring-boot-starter-plugin-divide</artifactId>
            <exclusions>
                <!-- 排除ShenYu自带的Netty依赖，使用我们统一管理的版本 -->
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.projectreactor.netty</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 显式添加统一版本的Netty和Reactor Netty依赖 -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>

        <dependency>
            <groupId>io.projectreactor.netty</groupId>
            <artifactId>reactor-netty</artifactId>
        </dependency>

        <!-- 添加WebFlux支持以确保ReactiveElasticsearchClient正常工作 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <!-- 暂时移除HttpClient插件，避免Reactor Netty兼容性问题 -->
        <!--
        <dependency>
            <groupId>org.apache.shenyu</groupId>
            <artifactId>shenyu-spring-boot-starter-plugin-httpclient</artifactId>
            <version>2.4.3</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.projectreactor.netty</groupId>
                    <artifactId>reactor-netty</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        -->



        <!-- Sharding-JDBC Spring Boot Starter -->
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
            <version>4.1.1</version>
        </dependency>




    </dependencies>



    <profiles>
        <profile>
            <id>swagger</id>
            <properties>
                <profile.swagger>,swagger</profile.swagger>
            </properties>
        </profile>

        <profile>
            <id>dev</id>
            <properties>
                <package.environment>dev</package.environment>
            </properties>
            <!-- 是否默认 true表示默认-->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>

        <profile>
            <id>test</id>
            <properties>
                <package.environment>test</package.environment>
            </properties>
        </profile>

        <profile>
            <!-- master分支的test环境 -->
            <id>release</id>
            <properties>
                <package.environment>release</package.environment>
            </properties>
        </profile>
        <profile>
            <!-- 人大进仓 -->
            <id>kingbase</id>
            <properties>
                <package.environment>kingbase</package.environment>
            </properties>
        </profile>
        <profile>
            <!-- 生产环境 -->
            <id>prod</id>
            <properties>
                <package.environment>prod</package.environment>
            </properties>
        </profile>

        <profile>
            <!-- hnszoa生产环境 -->
            <id>hnszoaprod</id>
            <properties>
                <package.environment>hnszoaprod</package.environment>
            </properties>
        </profile>

        <profile>
            <!-- 郴州会议 -->
            <id>czhy</id>
            <properties>
                <package.environment>czhy</package.environment>
            </properties>
        </profile>
    </profiles>


    <build>
        <defaultGoal>spring-boot:run</defaultGoal>
        <resources>
            <resource>
                <directory>${basedir}/src/main/webapp</directory>
                <!--注意此次必须要放在此目录下才能被访问到-->
                <targetPath>META-INF/resources</targetPath>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <includes>
                    <include>**/**</include>
                </includes>
                <excludes>
                    <exclude>**/license.lc</exclude>
                </excludes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <includes>
                    <include>**/license.lc</include>
                    <include>**/tomcat.jks</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>${basedir}/src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>application-${package.environment}.yml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <!--<plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.4.0-ctsi</version>
                <dependencies>

                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>5.1.45</version>
                    </dependency>

                    <dependency>
                        <groupId>com.ctsi.ssdc</groupId>
                        <artifactId>generator</artifactId>
                        <version>4.3.0</version>
                    </dependency>


                </dependencies>
            </plugin>-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>1.4.2.RELEASE</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                    <mainClass>com.ctsi.HndxoaApiApplication</mainClass>
                    <layout>ZIP</layout>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>1.3.6</version>
                <configuration>
                    <repository>${imageName}</repository>
                    <tag>${imageVersion}</tag>
                    <buildArgs>
                        <WAR_FILE>target/${project.build.finalName}.war</WAR_FILE>
                    </buildArgs>
                    <useMavenSettingsForAuth>true</useMavenSettingsForAuth>
                </configuration>
            </plugin>
        </plugins>


    </build>


</project>
