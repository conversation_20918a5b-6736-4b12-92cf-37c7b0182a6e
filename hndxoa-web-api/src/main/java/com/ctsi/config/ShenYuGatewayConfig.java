package com.ctsi.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * ShenYu网关客户端状态监控配置类
 * 用于监控和记录ShenYu网关的启用状态
 *
 * 使用方法：
 * 1. 启用网关：设置 shenyu.register.enabled=true 和 shenyu.client.http.enabled=true
 * 2. 禁用网关：设置 shenyu.register.enabled=false 和 shenyu.client.http.enabled=false
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Configuration
public class ShenYuGatewayConfig {

    private static final Logger logger = LoggerFactory.getLogger(ShenYuGatewayConfig.class);

    @Value("${shenyu.register.enabled:false}")
    private boolean registerEnabled;

    @Value("${shenyu.client.http.enabled:false}")
    private boolean clientEnabled;

    @PostConstruct
    public void logShenYuStatus() {
        logger.info("=== ShenYu网关客户端状态检查 ===");
        logger.info("注册功能 (shenyu.register.enabled): {}", registerEnabled);
        logger.info("客户端功能 (shenyu.client.http.enabled): {}", clientEnabled);

        if (registerEnabled || clientEnabled) {
            logger.info("✅ ShenYu网关客户端已启用");
        } else {
            logger.info("❌ ShenYu网关客户端已禁用");
        }
        logger.info("=====================================");
    }
}
