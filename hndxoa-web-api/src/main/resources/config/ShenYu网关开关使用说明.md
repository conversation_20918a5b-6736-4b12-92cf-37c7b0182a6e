# ShenYu网关开关使用说明

## 概述
本项目已集成Apache ShenYu网关客户端，可以通过配置文件灵活控制是否连接到ShenYu网关。

## 配置说明

### 关键配置项
```yaml
shenyu:
  register:
    enabled: true/false  # 注册功能开关 - 关键配置
    registerType: http
    serverLists: http://localhost:9095
    props:
      username: admin
      password: 123456
  client:
    http:
      enabled: true/false  # 客户端功能开关 - 关键配置
      props:
        contextPath: /api
        appName: hndxoa-api
        port: 9003
        isFull: false
```

### 核心开关
- `shenyu.register.enabled`: 控制是否向ShenYu Admin注册服务
- `shenyu.client.http.enabled`: 控制是否启用HTTP客户端功能

## 不同环境配置

### 开发环境 (application-dev.yml)
```yaml
shenyu:
  register:
    enabled: true      # 启用注册功能
  client:
    http:
      enabled: true    # 启用客户端功能
```

### 测试环境 (application-xcy.yml)
```yaml
shenyu:
  register:
    enabled: false     # 禁用注册功能
  client:
    http:
      enabled: false   # 禁用客户端功能
```

### 生产环境 (application-prod.yml)
```yaml
shenyu:
  register:
    enabled: false     # 禁用注册功能
  client:
    http:
      enabled: false   # 禁用客户端功能
```

## 使用方法

### 启用ShenYu网关
1. 修改对应环境的配置文件
2. 设置 `shenyu.register.enabled=true`
3. 设置 `shenyu.client.http.enabled=true`
4. 重启应用

### 禁用ShenYu网关
1. 修改对应环境的配置文件
2. 设置 `shenyu.register.enabled=false`
3. 设置 `shenyu.client.http.enabled=false`
4. 重启应用

## 状态监控

应用启动时会在日志中显示ShenYu网关的状态：

```
=== ShenYu网关客户端状态检查 ===
注册功能 (shenyu.register.enabled): false
客户端功能 (shenyu.client.http.enabled): false
❌ ShenYu网关客户端已禁用
=====================================
```

## 注意事项

1. **重启生效**: 修改配置后需要重启应用才能生效
2. **依赖检查**: 确保ShenYu Admin服务可访问（如果启用了网关）
3. **端口冲突**: 确保配置的端口没有被其他服务占用
4. **网络连通**: 确保应用服务器能够访问ShenYu Admin服务器

## 故障排查

### 问题1: 设置为false但仍然连接网关
**解决方案**: 检查以下配置项是否都设置为false：
- `shenyu.register.enabled=false`
- `shenyu.client.http.enabled=false`

### 问题2: 启动时报连接错误
**解决方案**: 
1. 检查ShenYu Admin服务是否启动
2. 检查网络连通性
3. 检查配置的服务器地址和端口是否正确

### 问题3: 服务注册失败
**解决方案**:
1. 检查用户名密码是否正确
2. 检查ShenYu Admin的认证配置
3. 查看ShenYu Admin的日志

## 配置文件位置

- 开发环境: `src/main/resources/config/application-dev.yml`
- 测试环境: `src/main/resources/config/application-xcy.yml`
- 生产环境: `src/main/resources/config/application-prod.yml`

## 相关类文件

- 配置监控类: `com.ctsi.config.ShenYuGatewayConfig`
