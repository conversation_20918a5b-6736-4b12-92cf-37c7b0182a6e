<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hndxoa</artifactId>
        <groupId>com.ctsi.hndxoa</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ctsi.hndxoa</groupId>
    <artifactId>hndxoa-userorg</artifactId>
    <version>${hndxoa.userorg.version}</version>
    <packaging>jar</packaging>
    <name>hndxoa-userorg</name>
    <description>用户信息管理</description>


    <dependencies>
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-import</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-sms</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <!-- 其他依赖 -->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.15</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/WST/commons-codec-1.15.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/WST/dom4j-2.1.3.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
            <version>1.2.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/WST/jaxen-1.2.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>1.4.10</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/WST/kotlin-stdlib-1.4.10.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/WST/okhttp-4.9.3.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.squareup.okio</groupId>
            <artifactId>okio</artifactId>
            <version>2.8.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/WST/okio-2.8.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <version>3.1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/WST/servlet-api.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>trustsystem-toolkit</groupId>
            <artifactId>trustsystem-toolkit</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../lib/WST/trustsystem-toolkit-1.0.0.jar</systemPath>
        </dependency>

        <!-- shenyu网关上传服务状态 - 降级到更兼容的版本 -->
        <dependency>
            <groupId>org.apache.shenyu</groupId>
            <artifactId>shenyu-spring-boot-starter-client-springmvc</artifactId>
            <version>2.6.1</version>
            <!-- 排除可能冲突的依赖 -->
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.projectreactor.netty</groupId>
                    <artifactId>reactor-netty</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 暂时移除HttpClient插件，避免Reactor Netty兼容性问题 -->
        <!--
        <dependency>
            <groupId>org.apache.shenyu</groupId>
            <artifactId>shenyu-spring-boot-starter-plugin-httpclient</artifactId>
            <version>2.4.3</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.projectreactor.netty</groupId>
                    <artifactId>reactor-netty</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        -->

    </dependencies>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

</project>