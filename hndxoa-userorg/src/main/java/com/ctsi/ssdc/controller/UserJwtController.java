package com.ctsi.ssdc.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.constant.HeaderConstants;
import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.encryption.Base64Encrypt;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.enums.UserExamineStatusEnum;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.systenant.service.impl.OnlinePeopleNumberService;
import com.ctsi.hndx.systenant.service.impl.TSysTenantServiceImpl;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.IpUtil;
import com.ctsi.hndx.utils.JsonUtils;
import com.ctsi.hndx.utils.UserAESUtil;
import com.ctsi.hndx.westone.WestoneEncryptService;
import com.ctsi.ssdc.admin.cache.LoginCache;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.dto.*;
import com.ctsi.ssdc.admin.entity.CscpUserLockRecord;
import com.ctsi.ssdc.admin.repository.CscpUserLockRecordMapper;
import com.ctsi.ssdc.admin.service.CscpMenusService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.captcha.BiyiCaptcha;
import com.ctsi.ssdc.log.SystemLogOperateQueue;
import com.ctsi.ssdc.log.SystemLogOperateStock;
import com.ctsi.ssdc.login.LoginContext;
import com.ctsi.ssdc.login.LoginEngine;
import com.ctsi.ssdc.login.RequestUserContextUtil;
import com.ctsi.ssdc.model.SystemLogOperation;
import com.ctsi.ssdc.model.UserForm;
import com.ctsi.ssdc.repository.SystemLogOperationMapper;
import com.ctsi.ssdc.security.CasAuthenticationToken;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.security.UserLoginValidator;
import com.ctsi.ssdc.security.UserNameAndSamePasswordAuthenticationToken;
import com.ctsi.ssdc.security.jwt.JWTConfigurer;
import com.ctsi.ssdc.security.jwt.TokenProvider;
import com.ctsi.ssdc.util.RSAUtil;
import com.ctsi.ssdc.util.RedisUtil;
import com.ctsi.ssdc.util.RequestUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zdww.biyi.component.sdk.aop.BeanExposeMethodAble;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.UnknownHostException;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import static com.ctsi.ssdc.admin.consts.ComponentConstant.ADMIN;
import static com.ctsi.ssdc.admin.consts.ComponentConstant.METHOD;

/**
 * Controller to authenticate users.
 */
@RestController
@RequestMapping("/api/system")
@Api(value = "登录相关接口", tags = "登录相关接口")
public class UserJwtController {

    private final Logger logger = LoggerFactory.getLogger(UserJwtController.class);

    private static final String UNAUTHORIZED = "Unauthorized";


    @Autowired(required = false)
    UserLoginValidator userLoginValidator;

    @Autowired
    CscpUserService cscpUserService;


    private final CscpMenusService cscpMenusService;

    @Value("${ctsi.RSA-prikey:}")
    private String rsaPrikey = "";

    private final TokenProvider tokenProvider;

    private final AuthenticationManager authenticationManager;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private OnlinePeopleNumberService onlinePeopleNumberService;


    @Autowired
    private TSysTenantServiceImpl tsystenantService;

//	@Resource(name = "redisUtil")
//	private RedisUtil redisUtil;

    @Autowired
    LoginCache loginCache;

    @Autowired
    PasswordEncoder passwordEncoder;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private CscpUserLockRecordMapper userLockRecordMapper;

    @Autowired
    private SystemLogOperationMapper systemLogOperationMapper;

    @Autowired
    private WestoneEncryptService westoneEncryptService;

    @Autowired
    private LoginEngine loginEngine;

    public UserJwtController(TokenProvider tokenProvider, AuthenticationManager authenticationManager, CscpMenusService cscpMenusService, CscpUserService cscpUserService) {
        this.tokenProvider = tokenProvider;
        this.authenticationManager = authenticationManager;
        this.cscpMenusService = cscpMenusService;
        this.cscpUserService = cscpUserService;
    }

    @BeanExposeMethodAble(component = ADMIN, method = "")
    @ApiOperation(value = "不需要cas登录即可", notes = "传入参数")
    @ApiImplicitParams({@ApiImplicitParam(name = "biyiCaptchaKey", value = "验证码key", paramType = "header", dataType = "String", defaultValue = ""), @ApiImplicitParam(name = "biyiCaptcha", value = "验证码", paramType = "header", dataType = "String", defaultValue = "{\"code\":\"1234\"}")})
    @BiyiCaptcha(rule = "defaultRule", service = "digitalCaptchaService")
    @ShenyuSpringMvcClient(path = "/login", desc = "用户登录接口")
    @PostMapping(value = "/login")
    public ResponseEntity<JwtToken> authorize(HttpServletRequest request, @Validated @RequestBody UserForm user) throws UnknownHostException {
        final Logger logger = LoggerFactory.getLogger(this.getClass());
        long totalStart = System.currentTimeMillis();

        try {
            // 1. 用户名解密
            long usernameDecryptStart = System.currentTimeMillis();
            Base64Encrypt base64Encrypt = new Base64Encrypt();
            String userName = user.getUserName();
            if (null != userName && !"".equals(userName)) {
                userName = UserAESUtil.decryptAES(userName);
            }
            long userNameDecryptCost = System.currentTimeMillis() - usernameDecryptStart;
            logger.info("Username decrypt cost: {}ms", NUMBER_FORMAT.get().format(userNameDecryptCost));

            // 管理员账号 IP判断（保留注释代码）
        /*
        if (userName.equalsIgnoreCase("admin") || userName.equalsIgnoreCase("aqbmgly")
                || userName.equalsIgnoreCase("aqsjgly")) {
            StringBuffer buffer = new StringBuffer(userName).append(":ip");
            SysConfigDTO sysConfigByCode = sysConfigService.getSysConfigByCode(buffer.toString());

            if (ObjectUtil.isNull(sysConfigByCode) || StringUtils.isEmpty(sysConfigByCode.getValue())
                    || StringUtils.isBlank(sysConfigByCode.getValue())) {
            } else {
                String realIp = IpUtil.getRealIp(request);
                if (StringUtils.isEmpty(realIp) || !sysConfigByCode.getValue().equals(realIp)) {
                    SystemLogOperation systemLogOperation = new SystemLogOperation();
                    systemLogOperation.setSystemType(1);
                    systemLogOperation.setUri("api/system/login");
                    systemLogOperation.setParams(JsonUtils.objectToJson(user));
                    systemLogOperation.setIp(realIp);
                    systemLogOperation.setOperationSource("协同办公平台");
                    systemLogOperation.setObjectBody("当前登录的用户" + userName + "绑定的ip不匹配");
                    systemLogOperation.setOperationType(2);
                    systemLogOperation.setOperationResult("失败");
                    logAsync(systemLogOperation);
                    throw new BusinessException("当前登录的用户和当前用户绑定的ip不匹配");
                }
            }
        }
        */

            // 2. 密码解密
            long passwordDecryptStart = System.currentTimeMillis();
            String userPassword = user.getPassword();
            if (null != userPassword && !"".equals(userPassword)) {
                userPassword = UserAESUtil.decryptAES(userPassword);
            }
            String password = decryptPassword(base64Encrypt.decrypt(userPassword));
            password = new StringBuilder(password).reverse().toString();
            long passwordDecryptCost = System.currentTimeMillis() - passwordDecryptStart;
            logger.info("Password decrypt cost: {}ms", NUMBER_FORMAT.get().format(passwordDecryptCost));

            boolean rememberMe = user.getRememberme() == 1;

            // 3. 用户查询
            long userQueryStart = System.currentTimeMillis();
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append("loginFailed:");
            stringBuffer.append(userName);
            String countKey = stringBuffer.toString();
            Integer countKeyValue = null;
            JSONObject jsonObject = JSON.parseObject(sysConfigService.getSysConfigValueByCode(SysConfigConstant.SECURITY_AUTHENTICATION));
            int badPasswordAttempts = jsonObject.getIntValue("passwordFailedLimit");
            int lockoutTime = jsonObject.getIntValue("loginLockTime") * 60;

            CscpUserDTO cscpUser = cscpUserService.getUserByUsernameOrMobile(userName);
            long userQueryCost = System.currentTimeMillis() - userQueryStart;
            logger.info("User query cost: {}ms", NUMBER_FORMAT.get().format(userQueryCost));

            if (cscpUser == null) {
                throw new BusinessException(ResultCode.USER_LOGIN_ERROR);
            }

            // 维护日志记录
            SystemLogOperation systemLogOperation = new SystemLogOperation();
            systemLogOperation.setSystemType(1);
            systemLogOperation.setUri("api/system/login");
            systemLogOperation.setParams(JsonUtils.objectToJson(user));
            systemLogOperation.setIp(IpUtil.getRealIp(request));
            systemLogOperation.setCreateBy(cscpUser.getId());
            systemLogOperation.setCreateName(cscpUser.getRealName());
            systemLogOperation.setOperationSource("协同办公平台");
            systemLogOperation.setMainBody(cscpUser.getLoginName());

            // 4. 缓存检查
            long cacheCheckStart = System.currentTimeMillis();
            if (loginCache != null) {
                countKeyValue = (Integer) loginCache.get(countKey);
                if (countKeyValue != null && countKeyValue == (badPasswordAttempts + 1)) {
                    long expire = loginCache.getExpire(countKey);
                    systemLogOperation.setObjectBody("密码输入错误达到上限锁定");
                    systemLogOperation.setOperationType(2);
                    systemLogOperation.setOperationResult("失败");
                    logAsync(systemLogOperation);
                    throw new BusinessException("密码输入错误次数达到{}次，请剩余{}分钟之后重试", badPasswordAttempts, expire / 60 == 0 ? 1 : expire / 60);
                }
            }
            long cacheCheckCost = System.currentTimeMillis() - cacheCheckStart;
            logger.info("Cache check cost: {}ms", NUMBER_FORMAT.get().format(cacheCheckCost));

            String token;

        /*
        if (!Objects.equals(cscpUser.getExamineStatus(), UserExamineStatusEnum.ExaminePassStatus.getCode())) {
            systemLogOperation.setObjectBody("您的账号未被激活请联系管理员");
            systemLogOperation.setOperationType(2);
            systemLogOperation.setOperationResult("失败");
            systemLogOperationMapper.insert(systemLogOperation);
            throw new BusinessException("您的账号未被激活请联系管理员");
        }
        */
            String realLoginName = cscpUser.getLoginName();

            // 5. 自定义验证
            if (userLoginValidator != null) {
                long validationStart = System.currentTimeMillis();
                userLoginValidator.validate(user);
                long customValidationCost = System.currentTimeMillis() - validationStart;
                logger.info("Custom validation cost: {}ms", NUMBER_FORMAT.get().format(customValidationCost));
            }

            // 6. 认证过程
            long authStart = System.currentTimeMillis();
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(realLoginName, password);
            Authentication authentication = null;
            try {
                authentication = this.authenticationManager.authenticate(authenticationToken);
            } catch (AuthenticationException e) {
                if (!(e instanceof InternalAuthenticationServiceException)) {
                    if (loginCache != null) {
                        int countKeyLeft = -1;
                        if (countKeyValue == null) {
                            loginCache.put(countKey, 1);
                            countKeyLeft = badPasswordAttempts;
                        } else if (countKeyValue <= badPasswordAttempts) {
                            loginCache.put(countKey, countKeyValue + 1);
                            countKeyLeft = badPasswordAttempts - countKeyValue;
                            if (countKeyLeft == 0) {
                                loginCache.put(countKey, badPasswordAttempts + 1);
                                // 用户被锁定 修改状态
                                cscpUserService.update(Wrappers.<CscpUser>lambdaUpdate()
                                        .set(CscpUser::getStatus, 0)
                                        .eq(CscpUser::getId, cscpUser.getId()));
                                // 维护锁定记录
                                CscpUserLockRecord cscpUserLockRecord = new CscpUserLockRecord();
                                cscpUserLockRecord.setUserId(cscpUser.getId());
                                cscpUserLockRecord.setLockTime(LocalDateTime.now());
                                cscpUserLockRecord.setLockIp(IpUtil.getRealIp(request));
                                userLockRecordMapper.insert(cscpUserLockRecord);

                                systemLogOperation.setObjectBody("密码输入错误达到上限锁定");
                                systemLogOperation.setOperationType(2);
                                systemLogOperation.setOperationResult("失败");
                                logAsync(systemLogOperation);

                                // 删除该账号token
                                redisUtil.batchDel("Bearer:pc*" + "(" + cscpUser.getId() + "*");
                                redisUtil.sSet("Bearer:pc:lock", cscpUser.getId());
                                throw new BusinessException("密码输入错误次数达到{}次，请{}分钟之后重试", badPasswordAttempts, lockoutTime / 60);
                            }
                        }
                        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(new JwtToken(countKeyLeft));
                    }
                    throw e;
                } else {
                    logger.error("-----登录认证失败:", e);
                    systemLogOperation.setObjectBody("登录异常，请确认用户密码是否正确");
                    systemLogOperation.setOperationType(2);
                    systemLogOperation.setOperationResult("失败");
                    logAsync(systemLogOperation);
                    throw new BusinessException("登录异常，请确认用户密码是否正确，" + e.toString());
                }
            } finally {
                long authenticationProcessCost = System.currentTimeMillis() - authStart;
                logger.info("Authentication process cost: {}ms", NUMBER_FORMAT.get().format(authenticationProcessCost));

                // 7. 清理缓存
                long cacheCleanStart = System.currentTimeMillis();
                if (loginCache != null && countKeyValue != null) {
                    loginCache.delete(countKey);
                }
                long cacheCleanCode = System.currentTimeMillis() - cacheCleanStart;
                logger.info("Cache clean cost: {}ms", NUMBER_FORMAT.get().format(cacheCleanCode));
            }

            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 8. Token生成
            long tokenGenStart = System.currentTimeMillis();
            String callSource = request.getHeader(HeaderConstants.CALL_SOURCE);
            token = tokenProvider.createToken(authentication, rememberMe, callSource);
            long tokenGenerationCost = System.currentTimeMillis() - tokenGenStart;
            logger.info("Token generation cost: {}ms", NUMBER_FORMAT.get().format(tokenGenerationCost));

            HttpHeaders httpHeaders = new HttpHeaders();

            // 9. 记录成功日志
            long successLogStart = System.currentTimeMillis();
            systemLogOperation.setObjectBody("登录成功");
            systemLogOperation.setOperationType(0);
            systemLogOperation.setOperationResult("成功");
            logAsync(systemLogOperation);
            long successLogCost = System.currentTimeMillis() - successLogStart;
            logger.info("Success log cost: {}ms", NUMBER_FORMAT.get().format(successLogCost));

            int status = 0;//cscpUserService.passwordNeedChange(SecurityUtils.getCurrentUserId(),password);
            if (status == 0) {
                httpHeaders.add(JWTConfigurer.AUTHORIZATION_HEADER, token);
                return new ResponseEntity<>(new JwtToken(token, status), httpHeaders, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(new JwtToken("", status), httpHeaders, HttpStatus.OK);
            }
        } finally {
            long totalCost = System.currentTimeMillis() - totalStart;
            logger.info("TOTAL authorize execution cost: {}ms", NUMBER_FORMAT.get().format(totalCost));
        }
    }

    /**
     * 登录接口（优化）
     */
    @BeanExposeMethodAble(component = ADMIN, method = "")
    @ApiOperation(value = "不需要cas登录即可", notes = "传入参数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "biyiCaptchaKey", value = "验证码key", paramType = "header", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "biyiCaptcha", value = "验证码", paramType = "header", dataType = "String", defaultValue = "{\"code\":\"1234\"}")
    })
    @BiyiCaptcha(rule = "defaultRule", service = "digitalCaptchaService")
    @PostMapping(value = "/login2")
    public ResponseEntity<JwtToken> authorize2(HttpServletRequest request, @Validated @RequestBody UserForm user) {
        long totalStart = System.currentTimeMillis();

        LoginContext context = new LoginContext();
        context.request = request;
        context.user = user;

        try {
            // 设置依赖
            context.loginCache = this.loginCache;
            context.redisUtil = this.redisUtil;

            // 执行命令流
            loginEngine.run(context);

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add(JWTConfigurer.AUTHORIZATION_HEADER, context.token);

            return new ResponseEntity<>(new JwtToken(context.token, 0), httpHeaders, HttpStatus.OK);

        } catch (Exception e) {
            logger.error("登录过程中发生异常", e);
            if (e instanceof BusinessException) {
                throw (BusinessException) e;
            } else {
                throw new BusinessException("登录过程中发生异常，请稍后重试");
            }
        } finally {
            RequestUserContextUtil.clearLoginContext();
            logger.debug("TOTAL authorize2 execution cost: {}ms", System.currentTimeMillis() - totalStart);
        }

    }

    @ApiOperation(value = "手机验证码登录", notes = "传入参数")
    @PostMapping(value = "/loginNew")
    public ResponseEntity<JwtToken> loginNew(HttpServletRequest request, @Validated @RequestBody UserForm user) {
        String userName = user.getUserName();
        //前端使用公钥进行加密，服务端私钥解密,私钥为空不解密
        if (null != userName && !"".equals(userName)) {
            userName = UserAESUtil.decryptAES(userName);
        }
        //如果登录类型是2加入验证码校验
        if (user.getLoginType() == 2) {
            String validCode = user.getValidCode();
            if (null != validCode && !"".equals(validCode)) {
                validCode = UserAESUtil.decryptAES(validCode);
            }
            Boolean aBoolean = cscpUserService.checkVerificationCode(SmsVerificationCodeDTO.builder().phone(userName).code(validCode).build());
            if (aBoolean) {
                throw new BusinessException("验证码错误!");
            }
        }
        boolean rememberMe = user.getRememberme() == 1;

        String jwt;
        CscpUserDTO cscpUserDTO = cscpUserService.getUserByUsernameOrMobile(userName);
        if (cscpUserDTO == null) {
            throw new BusinessException(ResultCode.USER_LOGIN_ERROR);
        }
        // 自定义验证
        if (userLoginValidator != null) {
            userLoginValidator.validate(user);
        }
        // security
        UserNameAndSamePasswordAuthenticationToken authenticationToken = new UserNameAndSamePasswordAuthenticationToken(cscpUserDTO.getLoginName(), cscpUserDTO.getPassword());
        Authentication authentication = null;
        try {
            authentication = this.authenticationManager.authenticate(authenticationToken);
        } catch (AuthenticationException e) {
            throw e;
        }

        String callSource = request.getHeader(HeaderConstants.CALL_SOURCE);

        SecurityContextHolder.getContext().setAuthentication(authentication);
        //生成jwt
        jwt = tokenProvider.createToken(authentication, rememberMe, callSource);


        HttpHeaders httpHeaders = new HttpHeaders();

        int status = 0;
        if (status == 0) {
            httpHeaders.add(JWTConfigurer.AUTHORIZATION_HEADER, jwt);

            //统计在线人数
            /*==============================本单位在线人数开始===========================================*/
            onlinePeopleNumberService.setOnlinePeopleNumber(callSource, jwt);
            /*==============================本单位在线人数结束===========================================*/
            String phone = SysConfigConstant.SMS_CODE + user.getUserName();
            redisUtil.del(phone);
            return new ResponseEntity<>(new JwtToken(jwt, status), httpHeaders, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new JwtToken("", status), httpHeaders, HttpStatus.OK);
        }
    }


    /**
     * 发送短信验证码
     *
     * @param smsVerificationCode
     * @return
     */
    @PostMapping("/sendSmsVerificationCodeWw")
    @ApiOperation(value = "发送短信验证码", notes = "传入参数")
    public ResultVO<Boolean> sendSmsVerificationCodeWw(@RequestBody SmsVerificationCodeDTO smsVerificationCode) {
        Boolean b = cscpUserService.sendSmsVerificationCodeWw(smsVerificationCode);
        return ResultVO.success(b);
    }


    // TODO 新版本app,安全传输加密
    @ApiOperation(value = "app或手机验证码登录，只校验手机号码和验证码，不校验密码", notes = "传入参数")
    @PostMapping(value = "/appLoginWwSec")
    public ResultVO<JwtToken> appLoginWwSec(HttpServletRequest request, HttpServletResponse response, @Validated @RequestBody UserForm user) {
        String userName = user.getUserName();
        if (null != userName && !"".equals(userName)) {
            userName = UserAESUtil.decryptAES(userName);
        }

        //如果登录类型是2加入验证码校验
        if (SysConfigConstant.CODE_LOGIN.equals(user.getLoginType().toString())) {
            String validCode = user.getValidCode();
            if (null != validCode && !"".equals(validCode)) {
                validCode = UserAESUtil.decryptAES(validCode);
            }
            Boolean aBoolean = cscpUserService.checkVerificationCode(SmsVerificationCodeDTO
                    .builder().phone(userName).code(validCode).build());
            if (aBoolean) {
                throw new BusinessException("验证码错误!");
            }
        }
        //前端使用公钥进行加密，服务端私钥解密,私钥为空不解密
        boolean rememberMe = user.getRememberme() == 1;

        String jwt;
        CscpUserDTO cscpUserDTO = cscpUserService.getUserByUsernameOrMobile(userName);
        if (cscpUserDTO == null) {
            throw new BusinessException(ResultCode.USER_NOT_EXIST);
        }

        // 自定义验证
        if (userLoginValidator != null) {
            userLoginValidator.validate(user);
        }
        // security
        Authentication authentication = null;
        try {
            if (SysConfigConstant.CODE_LOGIN.equals(user.getLoginType().toString())) {
                authentication = this.authenticationManager.authenticate(new UserNameAndSamePasswordAuthenticationToken(cscpUserDTO.getLoginName(), cscpUserDTO.getPassword()));
            } else {
                String userPassword = user.getPassword();
                if (null != userPassword && !"".equals(userPassword)) {
                    userPassword = UserAESUtil.decryptAES(userPassword);
                }
                String password = decryptPassword(new Base64Encrypt().decrypt(userPassword));
                password = new StringBuilder(password).reverse().toString();
                authentication = this.authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(cscpUserDTO.getLoginName(), password));
            }
        } catch (AuthenticationException e) {
//            throw e;
            throw new BusinessException(ResultCode.USER_LOGIN_ERROR);
        }
        String callSource = request.getHeader(HeaderConstants.CALL_SOURCE);

        // TODO 更新APP版本号
        updateAppVersion(cscpUserDTO, request);

        SecurityContextHolder.getContext().setAuthentication(authentication);
        jwt = tokenProvider.createToken(authentication, rememberMe, callSource);

        //统计在线人数
//        /*==============================本单位在线人数开始===========================================*/
//        onlinePeopleNumberService.setOnlinePeopleNumber(callSource, jwt);
//        /*==============================本单位在线人数结束===========================================*/

        String token = jwt;
        JwtToken jwtToken = new JwtToken(token, 0);

        response.setHeader(JWTConfigurer.AUTHORIZATION_HEADER, token);
        return ResultVO.success(jwtToken);

    }

    /**
     * 登录更新APP版本号
     * @param cscpUser 用户信息
     * @param request 请求信息
     */
    private void updateAppVersion(CscpUserDTO cscpUser, HttpServletRequest request) {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(securityContext);
            try {
                String appVersion = request.getHeader(HeaderConstants.APP_VERSION.toLowerCase());
                if (StringUtils.isBlank(appVersion)) {
                    appVersion = request.getHeader(HeaderConstants.APP_VERSION_V.toLowerCase());
                }
                if (org.apache.commons.lang3.StringUtils.isBlank(appVersion)) {
                    appVersion = request.getHeader(HeaderConstants.MY_APP_VERSION.toLowerCase());
                }
                if (StringUtils.isNotBlank(appVersion)) {
                    Integer appVersionNum = Integer.valueOf(appVersion);
                    LambdaUpdateWrapper<CscpUser> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(CscpUser::getId, cscpUser.getId()).set(CscpUser::getAppVersion, appVersionNum);
                    String appVersionName = request.getHeader(HeaderConstants.APP_VERSION_NAME.toLowerCase());
                    if (StringUtils.isNotBlank(appVersionName)) {
                        updateWrapper.set(CscpUser::getAppVersionName, appVersionName);
                    }
                    boolean update = cscpUserService.update(updateWrapper);
                    if (!update) {
                        logger.error("注意：app版本号未更新成功！login_name:{}，app_version：{}", cscpUser.getLoginName(), appVersionNum);
                    }
                }
            } catch (Exception ex) {
                logger.error("注意：app版本号更新失败！login_name:{}，异常信息：{}", cscpUser.getLoginName(), ex.getMessage());
            }
        });
    }

    @ApiOperation(value = "app登录，只校验手机号码，不校验密码", notes = "传入参数")
    @PostMapping(value = "/appLogin")
    public ResultVO<JwtToken> appLogin(HttpServletRequest request, HttpServletResponse response, @Validated @RequestBody UserForm user) {

        String userName = user.getUserName();
        //前端使用公钥进行加密，服务端私钥解密,私钥为空不解密
        boolean rememberMe = user.getRememberme() == 1;

        String jwt;
        CscpUserDTO cscpUserDTO = cscpUserService.getUserByUsernameOrMobile(userName);
        if (cscpUserDTO == null) {
            throw new BusinessException(ResultCode.USER_LOGIN_ERROR);
        }

        // 自定义验证
        if (userLoginValidator != null) {
            userLoginValidator.validate(user);
        }
        // security
        UserNameAndSamePasswordAuthenticationToken authenticationToken = new UserNameAndSamePasswordAuthenticationToken(cscpUserDTO.getLoginName(), cscpUserDTO.getPassword());
        Authentication authentication = null;
        try {
            authentication = this.authenticationManager.authenticate(authenticationToken);
        } catch (AuthenticationException e) {
            throw e;
        }
        String callSource = request.getHeader(HeaderConstants.CALL_SOURCE);

        SecurityContextHolder.getContext().setAuthentication(authentication);
        jwt = tokenProvider.createToken(authentication, rememberMe, callSource);

        // TODO 更新APP版本号
        updateAppVersion(cscpUserDTO, request);

        //统计在线人数
        /*==============================本单位在线人数开始===========================================*/
        onlinePeopleNumberService.setOnlinePeopleNumber(callSource, jwt);
        /*==============================本单位在线人数结束===========================================*/


        String token = jwt;
        response.setHeader(JWTConfigurer.AUTHORIZATION_HEADER, token);
        return ResultVO.success(new JwtToken(token));

    }


    @ApiOperation(value = "app登录，只校验手机号码，校验密码", notes = "传入参数")
    @PostMapping(value = "/appLoginNew")
    public ResultVO<JwtToken> appLoginNew(HttpServletRequest request, HttpServletResponse response, @Validated @RequestBody UserForm user) {
        Base64Encrypt base64Encrypt = new Base64Encrypt();

        String password = decryptPassword(base64Encrypt.decrypt(user.getPassword()));
        password = new StringBuilder(password).reverse().toString();
        String userName = user.getUserName();
        //前端使用公钥进行加密，服务端私钥解密,私钥为空不解密
        boolean rememberMe = user.getRememberme() == 1;

        String jwt;
        CscpUserDTO cscpUserDTO = cscpUserService.getUserByUsernameOrMobile(userName);
        if (cscpUserDTO == null) {
            throw new BusinessException(ResultCode.USER_LOGIN_ERROR);
        }

        // 自定义验证
        if (userLoginValidator != null) {
            userLoginValidator.validate(user);
        }
        // security
        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(cscpUserDTO.getLoginName(), password);
        Authentication authentication = null;
        try {
            authentication = this.authenticationManager.authenticate(authenticationToken);
        } catch (AuthenticationException e) {
            throw e;
        }
        String callSource = request.getHeader(HeaderConstants.CALL_SOURCE);

        SecurityContextHolder.getContext().setAuthentication(authentication);
        jwt = tokenProvider.createToken(authentication, rememberMe, callSource);

        // TODO 更新APP版本号
        updateAppVersion(cscpUserDTO, request);

        //统计在线人数
        /*==============================本单位在线人数开始===========================================*/
        onlinePeopleNumberService.setOnlinePeopleNumber(callSource, jwt);
        /*==============================本单位在线人数结束===========================================*/


        String token = jwt;
        response.setHeader(JWTConfigurer.AUTHORIZATION_HEADER, token);
        JwtToken resultData = new JwtToken(token);
        resultData.setStrId(cscpUserDTO.getStrId());
        return ResultVO.success(resultData);

    }


    /**
     * 省委单点登录，页面传来一个用户ID 查询用户基本信息调用登录
     * @param request
     * @param userStrId
     * @return
     */
    @PostMapping(value = "/swLoginPC")
    public ResponseEntity<JwtToken> swLoginPC(HttpServletRequest request, @RequestParam(value = "userStrId") String userStrId) throws Exception {
        Base64Encrypt base64Encrypt = new Base64Encrypt();
        LambdaQueryWrapper<CscpUser> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        if(StringUtils.isNotBlank(userStrId)) {
            lambdaQueryWrapper.eq(CscpUser::getStrId, userStrId);
        }
//        if(StringUtils.isNotBlank(loginName)) {
//            // 登录用户名称
//            //String decryptLoginName = AESOperator.getInstance().decrypt(loginName);
//            //logger.info("省委单点登录加密前用户名称:[{}],加密后用户名称:[{}]", loginName, decryptLoginName);
//            lambdaQueryWrapper.eq(CscpUser::getLoginName, loginName);
//        }
        CscpUser cscpUser = cscpUserService.selectOneNoAdd(lambdaQueryWrapper);

        // 维护日志记录
        SystemLogOperation systemLogOperation = new SystemLogOperation();
        systemLogOperation.setSystemType(1);
        systemLogOperation.setUri("api/system/swLoginPC");
        systemLogOperation.setParams(userStrId);
        systemLogOperation.setIp(IpUtil.getRealIp(request));
        systemLogOperation.setOperationSource("协同办公平台");

        if(cscpUser == null){
            systemLogOperation.setObjectBody("单点登录失败，" + ResultCode.USER_LOGIN_ERROR.message() + "，请联系管理员！");
            systemLogOperation.setOperationType(2);
            systemLogOperation.setOperationResult("失败");
//            systemLogOperationMapper.insert(systemLogOperation);
            logAsync(systemLogOperation);
            throw new BusinessException("单点登录失败，" + ResultCode.USER_LOGIN_ERROR.message() + "，请联系管理员！");
        }

        systemLogOperation.setCreateBy(cscpUser.getId());
        systemLogOperation.setCreateName(cscpUser.getRealName());
        systemLogOperation.setMainBody(cscpUser.getLoginName());

        if (!Objects.equals(cscpUser.getExamineStatus(), UserExamineStatusEnum.ExaminePassStatus.getCode())) {
            systemLogOperation.setObjectBody("单点登录失败, 您的账号未被激活请联系管理员");
            systemLogOperation.setOperationType(2);
            systemLogOperation.setOperationResult("失败");
//            systemLogOperationMapper.insert(systemLogOperation);
            logAsync(systemLogOperation);
            throw new BusinessException("您的账号未被激活请联系管理员");
        }
        // security
        UserNameAndSamePasswordAuthenticationToken authenticationToken =
                new UserNameAndSamePasswordAuthenticationToken(cscpUser.getLoginName(),
                        cscpUser.getPassword());
        Authentication authentication = null;
        try {
            authentication = this.authenticationManager.authenticate(authenticationToken);
        } catch (AuthenticationException e) {
            throw e;
        }

        SecurityContextHolder.getContext().setAuthentication(authentication);
        String callSource = request.getHeader(HeaderConstants.CALL_SOURCE);

        String jwt = tokenProvider.createToken(authentication, false, callSource);
        String token = jwt;
        HttpHeaders httpHeaders = new HttpHeaders();

        systemLogOperation.setObjectBody("单点登录成功");
        systemLogOperation.setOperationType(0);
        systemLogOperation.setOperationResult("成功");
//        systemLogOperationMapper.insert(systemLogOperation);
        logAsync(systemLogOperation);
        return new ResponseEntity<>(new JwtToken(token, 0), httpHeaders, HttpStatus.OK);
    }

    @Async(value = SysConstant.EXECUTOR_ASYNC_NAME)
    public void logAsync(SystemLogOperation systemLogOperation) {
        // TODO 开启线程计算系统日志HMAC内容，不影响主线程执行
        if (westoneEncryptService.isCipherMachine()) {
            SystemLogOperateQueue.getInstance().addBlockingQueueData(new SystemLogOperateStock(systemLogOperation, westoneEncryptService, systemLogOperationMapper));
        } else {
            systemLogOperationMapper.insert(systemLogOperation);
        }
    }

    /**
     * 手机端单点登录
     *
     * @param request
     * @param user
     * @return
     * @RequestParam(value = "userStrId") String userStrId,@RequestParam(value = "loginName") String loginName
     */
    @PostMapping(value = "/swLogin")
    public ResultVO<JwtToken> swLogin(HttpServletRequest request, HttpServletResponse response, @Validated @RequestBody UserForm user) throws Exception {
        String userStrId = user.getUserStrId();
        String loginName = user.getUserName();
        logger.info("srtID：{}, loginName:{}", userStrId, loginName);
        Base64Encrypt base64Encrypt = new Base64Encrypt();
        LambdaQueryWrapper<CscpUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(userStrId)) {
            lambdaQueryWrapper.eq(CscpUser::getStrId, userStrId);
            lambdaQueryWrapper.or().eq(CscpUser::getSjsStrId, userStrId);
        }
        if (StringUtils.isNotBlank(loginName)) {
            // 登录用户名称
            //String decryptLoginName = AESOperator.getInstance().decrypt(loginName);
            //logger.info("省委单点登录加密前用户名称:[{}],加密后用户名称:[{}]", loginName, decryptLoginName);
            lambdaQueryWrapper.eq(CscpUser::getLoginName, loginName);
        }
        CscpUser cscpUser = cscpUserService.selectOneNoAdd(lambdaQueryWrapper);

        if (cscpUser == null) {
            throw new BusinessException("用户在系统不存在，请联系管理员！");
        }
        // security
        UserNameAndSamePasswordAuthenticationToken authenticationToken = new UserNameAndSamePasswordAuthenticationToken(cscpUser.getLoginName(), cscpUser.getPassword());
        Authentication authentication = null;
        try {
            authentication = this.authenticationManager.authenticate(authenticationToken);
        } catch (AuthenticationException e) {
            throw e;
        }

        SecurityContextHolder.getContext().setAuthentication(authentication);
        String callSource = request.getHeader(HeaderConstants.CALL_SOURCE);

        // TODO 更新APP版本号
        CscpUserDTO cscpUserDTO = BeanConvertUtils.copyProperties(cscpUser, CscpUserDTO.class);
        updateAppVersion(cscpUserDTO, request);

        String jwt = tokenProvider.createToken(authentication, false, callSource);
//        String token = jwt;
//        HttpHeaders httpHeaders = new HttpHeaders();
//        return new ResponseEntity<>(new JwtToken(token, 0), httpHeaders, HttpStatus.OK);


        String token = jwt;
        response.setHeader(JWTConfigurer.AUTHORIZATION_HEADER, token);
        JwtToken resultData = new JwtToken(token);
        return ResultVO.success(resultData);
    }


    @GetMapping(value = "/refreshToken")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "刷新令牌")
    public ResponseEntity<JwtToken> refreshToken(@RequestParam int rememberme, HttpServletRequest request) {

        boolean rememberMe = rememberme == 1;

        List<GrantedAuthority> grantedAuthorities = new ArrayList<>();
        cscpMenusService.findByUserId(SecurityUtils.getCurrentUserId()).forEach(menu -> {

            if (StringUtils.isNotEmpty(menu.getPermissionCode())) {
                grantedAuthorities.add(new SimpleGrantedAuthority(menu.getPermissionCode()));
            }

        });

        Authentication currentAuth = SecurityContextHolder.getContext().getAuthentication();
        UsernamePasswordAuthenticationToken newAuthentication = new UsernamePasswordAuthenticationToken(currentAuth.getPrincipal(), currentAuth.getCredentials(), grantedAuthorities);

        String jwt = tokenProvider.createToken(newAuthentication, rememberMe, request.getHeader(HeaderConstants.CALL_SOURCE));

        HttpHeaders httpHeaders = new HttpHeaders();
        String token = jwt;

        httpHeaders.add(JWTConfigurer.AUTHORIZATION_HEADER, token);
        return new ResponseEntity<>(new JwtToken(token), httpHeaders, HttpStatus.OK);

    }

    @PostMapping(value = "/loginByCas")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    public ResponseEntity<JwtToken> loginByCas(@RequestParam String serviceUrl, @RequestParam String ticket, HttpServletRequest request) {

        boolean rememberMe = false;

        CasAuthenticationToken authenticationToken = new CasAuthenticationToken(serviceUrl, ticket, null);

        Authentication authentication = this.authenticationManager.authenticate(authenticationToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);

        String jwt = tokenProvider.createToken(authentication, rememberMe, request.getHeader(HeaderConstants.CALL_SOURCE));

        SecurityUtils.getOptionalCurrentUserId().map(userId -> {
            cscpUserService.updateUserDetailForLogin(userId);
            return null;
        });

        HttpHeaders httpHeaders = new HttpHeaders();
        String token = jwt;

        httpHeaders.add(JWTConfigurer.AUTHORIZATION_HEADER, token);
        return new ResponseEntity<>(new JwtToken(token), httpHeaders, HttpStatus.OK);
    }

    /**
     * 解密
     *
     * @param ciphertext 密码的密文
     * @return
     */
    private String decryptPassword(String ciphertext) {
        String password = ciphertext;
        try {
            if (StringUtils.isNotBlank(rsaPrikey)) {
                password = new String(RSAUtil.decryptPri(Base64.getDecoder().decode(ciphertext), Base64.getDecoder().decode(rsaPrikey)), "UTF-8");
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return password;
    }

    /**
     * 反转字符串
     * 该方法主要用于反转给定的字符串
     *
     * @param s 待反转的字符串如果字符串为空或长度小于等于1，则直接返回原字符串
     * @return 反转后的字符串
     */
    public String reverseString(String s) {
        if (s == null || s.length() <= 1) {
            return s;
        }
        char[] chars = s.toCharArray();
        for (int i = 0, j = chars.length - 1; i < j; i++, j--) {
            char temp = chars[i];
            chars[i] = chars[j];
            chars[j] = temp;
        }
        return new String(chars);
    }

    /**
     * Object to return as body in JWT Authentication.
     */
    public static class JwtToken {

        private String token;


        //
        private int passwordStatus;

        private int lockoutTime;

        private int attempts;

        /**
         * 商信ID
         */
        private String strId;

        public String getStrId() {
            return strId;
        }

        public void setStrId(String strId) {
            this.strId = strId;
        }

        JwtToken(String token) {
            this.token = token;
        }


        public JwtToken(String token, int passwordStatus) {
            this.token = token;
            this.passwordStatus = passwordStatus;
        }

        public JwtToken(int lockoutTime, int attempts) {
            this.lockoutTime = lockoutTime;
            this.attempts = attempts;
        }

        public JwtToken(int attempts) {
            this.attempts = attempts;
        }

        @JsonProperty("token")
        String getToken() {
            return token;
        }

        void setToken(String token) {
            this.token = token;
        }

        @JsonProperty("passwordStatus")
        int getPasswordStatus() {
            return passwordStatus;
        }

        void setPasswordStatus(int passwordStatus) {
            this.passwordStatus = passwordStatus;
        }

        @JsonProperty("lockoutTime")
        public int getLockoutTime() {
            return lockoutTime;
        }

        public void setLockoutTime(int lockoutTime) {
            this.lockoutTime = lockoutTime;
        }

        @JsonProperty("attempts")
        public int getAttempts() {
            return attempts;
        }

        public void setAttempts(int attempts) {
            this.attempts = attempts;
        }
    }


    /**
     * 获取本单位在线人数和未在线人数
     *
     * @return
     */
    @GetMapping("/statisticsSignInCompanyPeople")
    @ApiOperation(value = "获取本单位在线人数和未在线人数", notes = "传入参数")
    public ResultVO<StatisticsSignInPeopleDTO> statisticsSignInCompanyPeople() {
        return ResultVO.success(cscpUserService.statisticsSignInCompanyPeople());
    }


    /**
     * 获取租户在线人数和未在线人数
     *
     * @return
     */
    @GetMapping("/statisticsSignInTenantPeople")
    @ApiOperation(value = "获取租户在线人数和未在线人数", notes = "传入参数")
    public ResultVO<CscpStatisticsSignInTenantDTO> statisticsSignInTenantPeople() {
        return ResultVO.success(cscpUserService.statisticsSignInTenantPeople());
    }


    /**
     * 获取租户在线人数和本单位在线人数
     *
     * @return
     */
    @GetMapping("/numberOfPeopleOnline")
    @ApiOperation(value = "获取租户在线人数和本单位在线人数", notes = "传入参数")
    public ResultVO<NumberOfPeopleOnlineDTO> numberOfPeopleOnline() {
        return ResultVO.success(cscpUserService.numberOfPeopleOnline());
    }

    /**
     * 发送短信验证码
     *
     * @param smsVerificationCode
     * @return
     */
    @PostMapping("/sendSmsVerificationCode")
    @ApiOperation(value = "发送短信验证码", notes = "传入参数")
    public ResultVO<Boolean> sendSmsVerificationCode(@RequestBody SmsVerificationCodeDTO smsVerificationCode) {
        Boolean b = cscpUserService.sendSmsVerificationCode(smsVerificationCode);
        return ResultVO.success(b);
    }

    /**
     * 验证手机验证码是否正确
     *
     * @param smsVerificationCode
     * @return
     */
    @PostMapping("/checkVerificationCode")
    @ApiOperation(value = "验证手机验证码是否正确", notes = "传入参数")
    public ResultVO<Boolean> checkVerificationCode(@RequestBody @Valid SmsVerificationCodeDTO smsVerificationCode) {
        Boolean b = cscpUserService.checkVerificationCode(smsVerificationCode);
        return ResultVO.success(b);
    }

    /**
     * 查询多条数据.不分页
     */
    @GetMapping("/existMobileByMobileList")
    @ApiOperation(value = "查询多条数据", notes = "传入参数")
    ////@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<List> existMobileByMobileList(String mobiles, Long userId) {
        Assert.notNull(mobiles, "general.mobilesNotNull");
        List<String> list = cscpUserService.existMobileByMobileList(mobiles, userId);
        return ResultVO.success(list);
    }

    // 使用ThreadLocal缓存格式化对象
    private static final ThreadLocal<NumberFormat> NUMBER_FORMAT = ThreadLocal.withInitial(() -> {
        NumberFormat nf = NumberFormat.getInstance();
        nf.setMaximumFractionDigits(2);
        return nf;
    });

}
