package com.ctsi.ssdc.service;

import com.ctsi.ssdc.entity.dto.BizHrsUserInfoDTO;
import com.ctsi.ssdc.entity.BizHrsUserInfo;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.entity.dto.BizHrsUserInfoDetailDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 人社厅人员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public interface IBizHrsUserInfoService extends SysBaseServiceI<BizHrsUserInfo> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizHrsUserInfoDTO> queryListPage(BizHrsUserInfoDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    PageResult<BizHrsUserInfoDTO> queryHrsUnitPage(BizHrsUserInfoDTO entity, BasePageForm basePageForm);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizHrsUserInfoDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizHrsUserInfoDTO create(BizHrsUserInfoDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizHrsUserInfoDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizHrsUserInfoId
     * @param code
     * @return
     */
    boolean existByBizHrsUserInfoId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizHrsUserInfoDTO> dataList);


    PageResult<BizHrsUserInfoDetailDTO> queryBizHrsUserInfoPage(BizHrsUserInfoDTO bizHrsUserInfoDTO, BasePageForm basePageForm);

    /**
     * 同步人社厅用户信息到系统用户表
     *
     * @param hrsUnitName 人社厅选中的单位名称
     * @param deptName 系统已存在的部门名称
     * @return 更新成功的记录数
     */
    List<BizHrsUserInfoDTO> syncUserInfo(String hrsUnitName, String deptName);

    /**
     * 导出所有人社厅人员数据，按单位分组生成Excel文件并打包下载
     *
     * @param response HTTP响应对象
     * @return 是否导出成功
     */
    Boolean exportAllUsersByUnit(javax.servlet.http.HttpServletResponse response);
}
